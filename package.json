{"name": "cdc-exam-miniprogram", "version": "1.0.0", "description": "疾控医护任职资格考试系统微信小程序", "main": "main.js", "scripts": {"dev": "cross-env NODE_ENV=development uniapp-cli dev", "build": "cross-env NODE_ENV=production uniapp-cli build", "build:mp-weixin": "cross-env NODE_ENV=production uniapp-cli build --platform mp-weixin", "lint": "eslint --ext .js,.ts,.vue --ignore-path .gitignore .", "lint:fix": "eslint --ext .js,.ts,.vue --ignore-path .gitignore . --fix", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0-alpha-3060920220707001", "@dcloudio/uni-components": "^3.0.0-alpha-3060920220707001", "@dcloudio/uni-mp-weixin": "^3.0.0-alpha-3060920220707001", "vue": "^3.3.0", "pinia": "^2.1.0", "luch-request": "^3.1.0", "uv-ui": "^1.0.0"}, "devDependencies": {"@dcloudio/types": "^3.3.0", "@dcloudio/uni-automator": "^3.0.0-alpha-3060920220707001", "@dcloudio/uni-cli-shared": "^3.0.0-alpha-3060920220707001", "@dcloudio/vite-plugin-uni": "^3.0.0-alpha-3060920220707001", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "@vue/eslint-config-typescript": "^11.0.0", "cross-env": "^7.0.3", "eslint": "^8.0.0", "eslint-plugin-vue": "^9.0.0", "prettier": "^2.8.0", "sass": "^1.56.0", "typescript": "^4.9.0", "vue-tsc": "^1.0.0"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}