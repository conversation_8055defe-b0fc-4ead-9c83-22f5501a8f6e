/**
 * 本地存储工具函数
 */

/**
 * 设置本地存储
 * @param key 键名
 * @param value 值
 */
export function setStorage(key: string, value: unknown): void {
  try {
    uni.setStorageSync(key, value);
  } catch (error) {
    console.error('设置本地存储失败:', error);
  }
}

/**
 * 获取本地存储
 * @param key 键名
 * @param defaultValue 默认值
 */
export function getStorage<T = unknown>(key: string, defaultValue?: T): T | undefined {
  try {
    const value = uni.getStorageSync(key);
    return value !== '' ? value : defaultValue;
  } catch (error) {
    console.error('获取本地存储失败:', error);
    return defaultValue;
  }
}

/**
 * 移除本地存储
 * @param key 键名
 */
export function removeStorage(key: string): void {
  try {
    uni.removeStorageSync(key);
  } catch (error) {
    console.error('移除本地存储失败:', error);
  }
}

/**
 * 清空本地存储
 */
export function clearStorage(): void {
  try {
    uni.clearStorageSync();
  } catch (error) {
    console.error('清空本地存储失败:', error);
  }
}

/**
 * 获取存储信息
 */
export function getStorageInfo(): UniApp.GetStorageInfoSyncResult | null {
  try {
    return uni.getStorageInfoSync();
  } catch (error) {
    console.error('获取存储信息失败:', error);
    return null;
  }
}

/**
 * 带过期时间的存储
 */
export class ExpiringStorage {
  /**
   * 设置带过期时间的存储
   * @param key 键名
   * @param value 值
   * @param expireTime 过期时间（毫秒）
   */
  static set(key: string, value: unknown, expireTime: number): void {
    const data = {
      value,
      expireTime: Date.now() + expireTime,
    };
    setStorage(key, data);
  }
  
  /**
   * 获取带过期时间的存储
   * @param key 键名
   * @param defaultValue 默认值
   */
  static get<T = unknown>(key: string, defaultValue?: T): T | undefined {
    const data = getStorage(key) as { value: T; expireTime: number } | undefined;
    
    if (!data) {
      return defaultValue;
    }
    
    if (Date.now() > data.expireTime) {
      removeStorage(key);
      return defaultValue;
    }
    
    return data.value;
  }
  
  /**
   * 移除带过期时间的存储
   * @param key 键名
   */
  static remove(key: string): void {
    removeStorage(key);
  }
}

/**
 * 存储键名常量
 */
export const STORAGE_KEYS = {
  USER_PROFILE: 'user_profile',
  USER_TOKEN: 'user_token',
  APP_CONFIG: 'app_config',
  PRACTICE_STATS: 'practice_stats',
  EXAM_CACHE: 'exam_cache',
  QUESTION_CACHE: 'question_cache',
} as const;
