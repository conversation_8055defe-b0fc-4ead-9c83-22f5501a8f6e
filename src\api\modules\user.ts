/**
 * 用户相关API接口
 */
import http from '@/utils/request';
import type { UserInfo, LoginParams, RegisterParams } from '@/types/api';

/**
 * 微信登录
 */
export function wxLogin(params: LoginParams) {
  return http.post<UserInfo>('/auth/wx-login', params);
}

/**
 * 提交用户注册信息
 */
export function submitUserInfo(params: RegisterParams) {
  return http.post<boolean>('/user/register', params);
}

/**
 * 获取用户信息
 */
export function getUserInfo() {
  return http.get<UserInfo>('/user/profile');
}

/**
 * 上传用户头像
 */
export function uploadAvatar(file: File) {
  return http.upload<string>('/user/upload-avatar', { file });
}
