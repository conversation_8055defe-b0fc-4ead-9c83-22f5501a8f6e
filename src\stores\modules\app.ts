/**
 * 应用状态管理
 */
import { ref, computed } from 'vue';
import { defineStore } from 'pinia';

export const useAppStore = defineStore('app', () => {
  // State
  const loading = ref(false);
  const currentTabIndex = ref(0);
  const systemInfo = ref<UniApp.GetSystemInfoResult | null>(null);

  // Getters
  const isLoading = computed(() => loading.value);
  const statusBarHeight = computed(() => systemInfo.value?.statusBarHeight || 0);
  const screenHeight = computed(() => systemInfo.value?.screenHeight || 0);
  const screenWidth = computed(() => systemInfo.value?.screenWidth || 0);

  // Actions
  function setLoading(status: boolean) {
    loading.value = status;
  }

  function setCurrentTab(index: number) {
    currentTabIndex.value = index;
  }

  function initSystemInfo() {
    uni.getSystemInfo({
      success: (res) => {
        systemInfo.value = res;
      },
      fail: (error) => {
        console.error('获取系统信息失败:', error);
      },
    });
  }

  return {
    // State
    loading,
    currentTabIndex,
    systemInfo,
    // Getters
    isLoading,
    statusBarHeight,
    screenHeight,
    screenWidth,
    // Actions
    setLoading,
    setCurrentTab,
    initSystemInfo,
  };
});
