# 产品需求文档：疾控医护任职资格考试系统（小程序端）-2025年6月14日

版本：v1.1 | 日期：2025年6月14日

## 1. 产品概述

### 1.1 产品定位

一款专为疾控机构医护人员设计的任职资格考试及学习辅助平台。系统通过微信小程序（主要）及Web端，提供从学习备考、模拟练习、考试报名、在线考试到证书管理的全流程服务，旨在提升医护人员专业素养，并助力疾控机构实现高效、规范的人员资质管理。

### 1.2 目标用户

- **核心用户**：各级疾控机构在职医护人员。他们需要在规定周期内通过任职资格考试，以维持或提升其执业资质。
- **相关用户**：

  - **机构管理员**：负责本机构内医护人员的账号审核、考试资格配置、线下考试组织及成绩录入等（主要通过后台管理系统操作，小程序端涉及其审核结果的展示和部分交互）。

### 1.3 核心价值

- **对于医护人员**：

  - **便捷高效**：随时随地通过微信小程序进行学习、刷题、参与线上考试及报名线下考试。
  - **资源集中**：一站式获取考试公告、政策法规、学习资料（题库为主，教材预留）。
  - **智能辅助**：个性化的考试提醒、清晰的考试状态追踪、便捷的证书查询与管理。
- **对于疾控机构**：

  - **提升管理效率**：简化考试组织流程，减轻人工审核和通知负担。
  - **降低组织成本**：通过线上化手段减少线下考试的部分组织成本。
  - **数据驱动**：为人员培训和资质管理提供数据支持。

## 2. 需求背景

### 2.1 市场机会

- **行业政策驱动**：医疗卫生行业对从业人员的专业素养和执业资格有严格要求，定期进行任职资格考核是保障医疗服务质量和公共卫生安全的重要手段。相关政策的推动为专业化的考试系统提供了市场基础。
- **数字化转型趋势**：传统线下考试和培训模式在效率、成本和便捷性方面存在局限。利用移动互联网技术，特别是微信小程序的普及性，将资格考试流程数字化、移动化，是行业发展的必然趋势。
- **提升培训与考核效率的需求**：疾控机构面临着持续提升在职医护人员专业能力的挑战。一个高效、便捷的在线学习与考试平台，能够显著提高培训覆盖率和考核组织效率。
- **现有解决方案的不足**：市场上通用的在线考试系统可能无法完全契合疾控行业的特定需求（如机构化管理、特定题库类型、线上线下结合的考试模式等），定制化或针对性开发的系统具有竞争优势。

### 2.2 用户痛点

- **对于医护人员**：

  - **备考不便**：工作繁忙，排班不定，难以抽出固定、集中的时间参与线下培训和复习。
  - **学习资源分散**：官方教材、复习题库、政策文件等学习资料查找困难，缺乏系统性。
  - **练习反馈滞后**：传统练习方式缺乏即时反馈和针对性指导，影响备考效率。
  - **考试流程繁琐**：线下考试报名、资格确认、信息获取等环节耗时耗力。
  - **信息获取不及时**：考试通知、政策更新等重要信息可能无法第一时间掌握。
  - **证书管理不便**：纸质证书易丢失，历史证书查询和效期管理麻烦。
- **对于疾控机构**：

  - **组织成本高昂**：组织线下培训和考试涉及场地、物料、监考、阅卷等，人力和时间成本高。
  - **管理效率低下**：人员信息管理、考试资格审核、成绩统计、证书发放等工作手动处理繁琐，易出错。
  - **培训效果难追踪**：难以有效评估培训效果和学员掌握程度。
  - **通知触达局限**：重要通知和政策文件下发效率不高，覆盖面有限。
  - **数据利用不足**：缺乏系统化的数据支持，难以对人员能力水平和培训需求进行有效分析。

### 2.3 竞品分析

目前市场上可能存在以下几类解决方案：

- **通用在线考试平台**：功能全面，但可能缺乏针对疾控行业的定制化内容和流程，如特定题库分类、机构审核机制、与线下疾控工作流程的深度融合等。
- **传统线下培训及考试**：权威性高，但存在上述用户痛点中提到的时间、地点不便，组织成本高等问题。
- **部分机构自行开发的零散工具**：可能解决了部分问题，但缺乏系统性、稳定性和持续迭代能力，用户体验参差不齐。

本系统通过深度结合疾控行业的业务特点和用户需求，提供一站式、便捷化、智能化的解决方案，旨在弥补现有方案的不足，为疾控医护人员及其管理机构创造独特价值。

## 3. 产品目标

### 3.1 业务目标

- **初期 (MVP阶段 - 未来3-6个月)** ：

  - **用户覆盖**：成功在至少 [1] 试点疾控机构推广使用，并达到 [70%] 以上目标医护人员注册和激活。
  - **核心功能验证**：完成核心学习（题库练习）、线上考试、线下考试预约、个人信息与证书管理功能的上线和稳定运行。
  - **提升效率**：通过线上化手段，初步降低试点机构组织线上考试和管理的平均时间和人力成本 [待填写：例如10-15%]。
- **中期 (未来6-12个月)** ：

  - **用户增长**：将系统推广至更多疾控机构，医护人员注册用户数达到 [待填写具体数量或增长率]。
  - **功能完善**：根据用户反馈和业务需求，迭代优化现有功能，引入“教材学习”模块，丰富题库内容和形式。
  - **提升通过率**：通过系统辅助学习，目标用户的平均任职资格考试首次通过率提升 [待填写：例如5-10%]。
  - **数据价值挖掘**：初步建立用户学习行为和考试数据分析能力，为机构管理提供数据洞察。
- **长期 (未来1-3年)** ：

  - **行业标杆**：成为区域内或特定领域疾控医护任职资格考试的首选平台。
  - **生态构建**：探索与更多医疗健康服务、继续教育资源的整合，拓展产品价值链。
  - **持续提升**：持续优化用户体验，提升系统智能化水平，保持行业领先。

### 3.2 用户目标

- **对于医护人员**：

  - **高效备考**：能够利用碎片化时间，便捷地进行在线学习、刷题练习，提高备考效率。
  - **顺利参考**：清晰了解考试安排，顺利完成线上考试和线下考试的报名与参与。
  - **便捷管理**：方便地查询个人信息、考试记录和电子证书，及时了解证书状态和有效期。
  - **及时获取信息**：快速获取考试公告、政策法规等重要资讯。
- **对于机构管理员 (通过本系统间接实现)** ：

  - **高效管理**：更便捷地向医护人员推送考试通知、管理考试资格。
  - **减轻负担**：减少人工处理报名、审核、成绩通知等事务性工作的负担。

### 3.3 成功指标

为衡量产品目标的达成情况，设定以下关键绩效指标 (KPIs)：

- **用户活跃度**：

  - 日活跃用户数 (DAU) / 月活跃用户数 (MAU)
  - 用户平均使用时长
  - 题库模块每日/每周练习次数
- **核心功能使用率**：

  - 线上考试参与率
  - 线下考试报名率
  - 证书查看/下载次数
- **用户满意度**：

  - 用户反馈评分 (通过“投诉与建议”或定期问卷收集)
  - 净推荐值 (NPS)
- **业务效率提升**：

  - （试点机构）考试组织平均耗时对比 (与传统方式)
  - （试点机构）医护人员考试通过率变化
  - 用户注册审核通过时长
- **系统稳定性与性能**：

  - 小程序平均加载时间
  - 关键页面崩溃率
  - API平均响应时间

## 4. 功能需求

### 4.1 功能架构图

```mermaid
graph TD
    A[用户] --> B{微信小程序}

    subgraph B [疾控医护任职资格考试系统（小程序端）]
        C[登录/注册模块]
        D[底部导航栏]
        E[信息中心]
        F[学习中心]
        G[考试中心]
        H[个人中心]

        C --> D
        D --> E
        D --> F
        D --> G
        D --> H

        subgraph C [登录/注册模块]
            C1[微信授权登录]
            C2[用户协议确认]
            C3[个人资料提交/注册]
            C3_1[跳过提交按钮]
            C3_2[本人照片上传]
            C4[审核状态查询]
        end

        subgraph E [信息中心]
            E1[公告列表]
            E2[政策法规列表]
            E3[重要通知列表]
            E4[内容详情查看]
            E5[非正式用户状态提示]
        end

	subgraph F [学习中心]
  	    F1[教材模块 （预留）]
  	    F2[题库模块]
            F2_1[题库分类选择: 接种门诊/产科/犬伤门诊]
            F2_2[刷题模式 （单选/多选/判断/问答）]
            F2_3[答案解析/参考答案]
            F2_4[练习次数限制 （免费3组x10题/天）]
            F2_5[VIP特权 （预留）]
        end

        subgraph G [考试中心]
            G0[非正式用户状态提示]
            G1[待考列表 -本期考试]
            G1_1[线上考试卡片]
            G1_1_1[考前阅读]
            G1_1_2[人脸识别-同步比对]
            G1_1_3[在线答题界面（含水印/禁复制）]
            G1_1_4[答题提交（不即时出分）]
            G1_1_5[考试状态显示]
            G1_1_6[防作弊（切屏/抓拍/录音预留）]
            G1_2[线下考试卡片]
            G1_2_1[考试详情查看（考场->时间列表）]
            G1_2_2[场次预约/报名]
            G1_2_3[取消预约]
            G1_2_4[考试状态显示]
            G2[历史考试记录]
            G2_1[记录列表]
            G2_2[记录详情]
        end

        subgraph H [个人中心]
            H0[顶部用户信息与状态]
            H1[个人信息展示/提交入口]
            H2[证书管理]
            H2_1[证书状态显示（审批中/已发放/旧证有效）]
            H2_2[当前证书查看]
            H2_3[历史证书列表/查看]
            H3[投诉与建议]
            H4[关于我们]
        end
    end

    B --> I{后端服务 （RuoYi-Vue修改版）}
    I --> J[数据库]
    I --> K[机构管理员后台]

    K -- 管理与配置 --> I
```

### 4.2 核心功能详述

#### **模块一：登录与注册**

**4.2.1 功能名称：微信授权登录与协议确认**

- **用户故事**：

  - 作为一名新用户，我希望可以通过微信一键授权快速登录系统。
  - 作为一名用户，我希望在首次登录时能够阅读并同意用户须知/隐私协议。
- **功能描述**：

  1. **启动页/登录页**：展示产品Logo、名称，"微信授权登录" 按钮，以及协议勾选框（“我已阅读并同意《用户服务协议》和《隐私政策》”）。默认不勾选，必须勾选才能登录。协议文本可点击查看详情。
  2. **微信授权**：用户勾选协议并点击登录按钮后，调用微信API拉起授权弹窗。同意后获取用户信息（OpenID/UnionID, 昵称, 头像）。拒绝则提示。
  3. **登录状态判断**：授权成功后，将微信信息发送至后端。后端判断用户状态（正式用户、待审核、审核不通过、未提交资料的新用户），返回对应信息和token。前端根据状态跳转至相应页面（信息中心或个人资料提交页）。
- **交互流程**：打开小程序 -> 登录页 -> 勾选协议 -> 点击微信授权登录 -> 微信授权弹窗 -> 允许 -> 获取信息 -> 调用后端登录 -> 后端判断状态 -> 前端跳转。
- **验收标准**：必须勾选协议；成功拉起授权并获取信息；后端正确判断状态；前端正确跳转；拒绝授权有提示；协议可查看。

**4.2.2 功能名称：个人资料提交 (注册)**

- **用户故事**：

  - 作为一名首次微信登录或未提交资料的用户，我希望能提交从业信息（姓名、联系方式、身份证、隶属机构、职位、本人照片）供机构审核。
  - 我希望在选择隶属机构和职位时能通过搜索快速定位。
  - 我希望能上传一张清晰的本人正面照片，以便在参加线上考试时进行人脸身份验证。
  - 我希望能暂时跳过资料提交，先体验部分功能。
- **功能描述**：

  1. **适用场景**：新微信用户登录后引导进入；或用户从个人中心主动发起/修改。
  2. **表单项**：

      - **姓名**：（文本，必填）。
      - **联系方式**：（手机号，必填，格式校验）。
      - **身份证号码**：（文本，必填，格式校验）。
      - **本人照片 (必填，用于人脸识别比对)** ：

        - **上传方式**：提供“上传照片”按钮/区域，点击调用 `wx.chooseImage`​ 从相册选择。
        - **照片要求提示**：清晰文字提示：“请上传本人近期正面免冠证件照或清晰的生活照，确保五官清晰可见，照片大小不超过200KB，用于线上考试人脸识别。”
        - **预览与裁剪 (可选但建议)** ：选择图片后提供预览，允许基础裁剪。
        - **大小与格式限制**：前端校验大小（<200KB），超限提示。支持JPEG, PNG。
        - **上传成功后**：显示缩略图，允许删除重传。
      - **隶属机构**：（下拉选择，后台列表，支持搜索，必填）。
      - **职位**：（下拉选择，后台列表，支持搜索，必填）。
  3. **操作按钮**：

      -  **“提交审核”按钮**。
      -  **“跳过，先去学习”按钮**：点击后，标记用户为“未提交资料”，跳转至学习中心或信息中心（信息中心会提示完善资料）。
  4. **信息提交**：点击“提交审核”前进行前端校验（包括照片已上传）。通过后，将表单数据（包含照片文件或其上传后的URL）及微信信息提交后端。后端保存资料，状态置为“待审核”。
  5. **提交后反馈**：成功提示“资料提交成功，请等待机构管理员审核”。页面跳转至个人中心或信息中心，显示“身份认证审核中”等提示。
- **交互流程**：进入页面 -> 填写表单（含上传照片并校验） -> （可选）点击“跳过” -> （或）点击“提交审核” -> 前端校验 -> 调用后端接口 -> 后端处理 -> 前端反馈与跳转。
- **验收标准**：表单项按要求展示和校验；机构/职位列表加载与搜索正常；“本人照片”上传功能正常，有要求提示，大小校验有效，上传后显示缩略图并可重传；照片能成功提交至后端；信息能成功提交，状态更新正确；提交/跳过操作反馈清晰，跳转正确；未通过审核用户可看到历史提交信息（方便修改）。

#### **模块二：底部导航栏与通用规则**

**4.2.3 功能名称：底部导航栏**

- **用户故事**：我希望小程序底部有清晰的导航栏，方便在主要功能区切换。
- **功能描述**：固定在页面底部，含四个标签页（图标+文字），从左到右：信息中心、学习中心、考试中心、个人中心。点击切换页面，当前选中标签高亮。登录成功后，初始页面为“信息中心”。
- **验收标准**：四标签顺序正确；点击切换正常；选中状态明显；登录后默认进入信息中心。

**4.2.4 功能名称：用户访问权限控制**

- **用户故事**：不同认证状态的用户，我希望能访问与其身份匹配的功能。
- **功能描述**：

  1. **未登录用户**：无法访问任何主功能区，引导至登录页。
  2. **已微信登录但未完成机构认证的用户（非正式用户）** ：

      - **状态细分**：未提交个人资料、已提交待审核、资料审核未通过。
      - **信息中心**：可访问，但内容区显示对应状态提示和操作引导（如“去提交资料”、“审核中”、“去修改资料”），不展示公告列表。
      - **学习中心**：三种子状态均可访问所有功能（刷题受免费次数限制）。
      - **考试中心**：可访问，但内容区显示“未认证，无法考试”提示，可引导至资料提交。
      - **个人中心**：可访问，但仅显示与当前状态相关的操作入口（如提交/修改资料、查看审核状态）及通用功能（投诉建议、关于我们）。不显示完整个人敏感信息和证书管理。
  3. **已微信登录且通过机构审核的用户（正式用户）** ：可访问小程序所有功能（学习中心VIP特权除外，需另外判断）。
- **实现方式建议**：后端返回用户精确状态，前端据此控制页面内容显示和功能可用性。
- **验收标准**：各状态用户权限控制准确，无越权；页面提示和引导符合逻辑。

#### **模块三：信息中心**

**4.2.5 功能名称：信息展示 (公告、政策、通知)**

- **用户故事**：我希望能快速查看到最新的考试公告、政策法规及重要通知，重要信息能醒目展示。
- **功能描述**：

  1. **内容来源**：后台推送。
  2. **页面布局**：

      - **非正式用户**：按4.2.4描述，显示身份状态提示和引导，不展示信息列表。
      - **正式用户**：可包含置顶/轮播区（可选，展示最重要信息）、公告列表、政策法规列表、重要通知列表。信息项显示标题、日期、摘要（可选）。
  3. **优先级体现**：置顶/轮播区高优；列表信息可根据后台设置加“[重要]”等标签或视觉强调；默认按发布日期倒序，置顶信息优先。
  4. **列表交互**：支持下拉刷新、上拉加载更多（分页）。每条信息可点击进入详情页。
  5. **信息详情页**：展示完整标题、日期、来源（可选）、正文（支持富文本）。
- **API接口需求 (示意)** ：`GET /api/announcements`​ (列表), `GET /api/announcements/{id}`​ (详情)
- **验收标准**：非正式用户提示正确；正式用户列表展示与优先级正确；刷新加载正常；详情页内容显示完整，富文本渲染正常。

#### **模块四：学习中心**

**4.2.6 功能名称：学习中心主页布局**

- **用户故事**：我希望进入学习中心能清晰看到“教材”和“题库”模块。
- **功能描述**：两大模块入口：**教材学习** (本次预留，点击提示“功能建设中”)、**题库练习** (醒目入口，点击进入分类选择)。
- **验收标准**：两大模块入口正确显示；教材模块按预留状态处理；题库入口可点击。

**4.2.7 功能名称：题库练习 - 分类选择**

- **用户故事**：开始刷题前，我希望能选择专业分类（如“接种门诊”、“产科”、“犬伤门诊”）。
- **功能描述**：从学习中心主页点击“题库练习”进入。页面以列表或卡片展示后台配置的题库分类（可显示名称、题目总数）。用户点击任一分类进入刷题。
- **API接口需求 (示意)** ：`GET /api/question_bank/categories`​
- **验收标准**：正确显示题库分类；点击分类进入对应刷题流程。

**4.2.8 功能名称：题库练习 - 刷题界面与交互**

- **用户故事**：我希望能进行单选、多选、判断、问答题的练习，并能立即看到答案和解析。
- **功能描述**：

  1. **默认规则**：每次练习随机抽取10道题为一组。
  2. **界面元素**：顶部（分类名、进度“X/10”）；题目区域（题干、选项/作答区）；底部（上一题/下一题按钮，最后一题为“提交本组练习”）。
  3. **题型支持**：单选（圆形按钮）、多选（方形复选框，提示“多选题”）、判断（“正确”/“错误”按钮）、问答（多行文本输入框，提示“问答题”）。
  4. **交互与反馈**：用户选择/填写答案后，**即时反馈模式**：立即显示该题是否正确（问答题除外）。高亮正确答案，并显示该题的“答案解析”或“参考答案”（默认折叠，可展开）。问答题提交后，直接显示参考答案和解析供用户自评。
  5. **练习完成**：答完10题或主动提交后，显示总结（答对数、错数、正确率）。提供“再练一组”、“返回题库分类”操作。
- **API接口需求 (示意)** ：`GET /api/question_bank/questions?category_id={id}&count=10`​
- **验收标准**：正确加载题目；各题型展示与交互符合标准；即时反馈与答案解析正确；练习总结准确。

**4.2.9 功能名称：题库练习 - 刷题次数限制 (免费与VIP)**

- **用户故事**：免费用户每天刷题次数有限制（3组），VIP用户可无限刷（VIP功能预留）。
- **功能描述**：

  1. **用户身份**：默认免费，VIP通过后台开通（支付功能预留）。
  2. **次数限制**：免费用户每天可进行3组练习（每组10题）。达到限制后提示“今日免费练习次数已用完。升级VIP可无限畅练，敬请期待！”。次数按自然日统计。VIP用户在有效期内无限制。
  3. **VIP支付预留**：相关提示中可提及VIP，但点击后提示“VIP功能即将开放”或无升级入口。后端预留VIP状态字段。
- **验收标准**：免费用户次数限制与提示正确；VIP支付按预留处理；VIP用户无限制（通过后台标记测试）。

#### **模块五：考试中心**

**4.2.10 功能名称：考试中心主页与权限**

- **用户故事**：正式用户希望能看到本期考试和历史记录；非正式用户应被告知无权限。
- **功能描述**：

  1. **访问权限**：非正式用户进入后提示“未认证，无法考试”并引导认证。正式用户可正常使用。
  2. **页面布局 (正式用户)** ：顶部/主要区域“本期考试（待考列表）”（默认展开）；底部/次要区域“历史考试记录”（默认折叠或为入口按钮）。
- **验收标准**：权限控制正确；正式用户页面布局符合描述。

**4.2.11 功能名称：本期考试 (待考列表) - 展示**

- **用户故事**：我希望能清晰看到本期需参加的线上/线下考试，及各自状态。
- **功能描述**：后台为用户配置的待考列表，以卡片形式展示。卡片含：考试名称、类型标签（线上/线下）、考试状态（线上：未参与/进行中/已通过/未通过/已结束/已过期；线下：未报名/已报名/已通过/未通过/已结束/已过期）、考试有效期、操作按钮（据状态动态显示，如“开始考试”、“立即报名”等）。列表可排序。无待考则提示。
- **API接口需求 (示意)** ：`GET /api/exams/current`​
- **验收标准**：正确显示用户待考列表；卡片信息完整准确；状态与操作按钮动态正确；空状态提示友好。

**4.2.12 功能名称：线上考试流程**

- **用户故事**：我希望线上考试流程包含考前阅读、人脸识别、在线答题、顺利交卷，并有防作弊措施。
- **功能描述**：  
  **A. 考前阅读**：点击“开始考试”后进入。展示后台配置的考前须知。用户阅读并确认后（可设倒计时或滚动到底）进入下一步。  
  **B. 人脸识别 (身份验证 - 同步等待比对结果)** ：调起前置摄像头拍照，上传至后端。前端显示等待提示，同步等待后端与预存基准照片（用户注册时提交）比对结果。通过则进入答题；不通过则提示重试（限3次），多次失败中止考试。  
  **C. 在线答题**：界面顶部（考试名、剩余时间倒计时、进度）；题目区域（同学习中心刷题，支持单选/多选/判断/问答）；底部/侧边（上一题/下一题、题目列表/答题卡入口、交卷按钮）。答案实时本地保存。时间到自动交卷。**界面叠加考生信息水印，题目区域限制复制粘贴。**   
  **D. 提交答卷**：点击“交卷”（有确认提示）或时间到。加密提交所有答案至后端。**提交后不即时出分**，提示“答卷提交成功！请耐心等待成绩公布。”，跳转页面。  
  **E. 防作弊机制**：**切屏警告与记录**（监听小程序onHide/onShow，记录次数时长，达限可强制交卷或标记）；**随机人脸抓拍**（考试中静默调用前置摄像头抓拍上传，需提前告知授权）；**后台录音**（预留接口，默认不启用，如用需显著告知授权）；**题目乱序**（后台支持）。
- **API接口需求 (示意)** ：`GET /exams/{id}/rules`​ (考规), `POST /exams/verify_face_identity`​ (人脸比对), `GET /exams/{id}/questions`​ (题目), `POST /exams/submit_answers`​ (交卷), `POST /exams/anticheat_log`​ (防作弊日志)
- **验收标准**：各环节流程完整顺畅；人脸识别同步比对逻辑正确；答题界面、水印、防复制粘贴符合要求；交卷与反馈正确；各项防作弊机制按要求实现和记录。

**4.2.13 功能名称：线上考试 - 重复考试逻辑**

- **用户故事**：考试未通过但在有效期内，希望能重考。
- **功能描述**：用户考试“未通过”、仍在有效期内、且后台允许重考（可设次数限制）。考试卡片操作按钮变为“重新考试”。点击后重走完整线上考试流程。每次重考为独立记录，最终成绩认定策略由后台定。
- **验收标准**：符合条件时“重新考试”按钮可用；能启动新考试；次数限制生效；成绩记录符合策略。

**4.2.14 功能名称：线下考试 - 报名与详情查看**

- **用户故事**：我希望能查看线下考试的考场、可选时间及名额，并进行报名或取消。
- **功能描述**：

  1. **入口**：点击“本期考试”列表中的线下考试卡片。
  2. **详情页**：考试基本信息（名称、说明）。**考场及场次列表**：先展示考场列表（若多个考场，可用标签页切换或卡片展开），再展示选定考场下的考试时间列表。时间列表含：日期、时间段、剩余名额/总名额、操作按钮（“报名”或“已报满”等，据名额和用户状态定）。
  3. **用户已报名信息**：清晰展示已选考场、日期、时间，及“取消报名”按钮（考虑取消时限）。
  4. **报名/取消逻辑**：用户选择有余额的考场和时间点报名，或取消已报名场次。前后端交互校验名额和条件，更新状态。
- **API接口需求 (示意)** ：`GET /exams/offline/{id}/venues_and_schedules`​ (考场与场次), `POST /exams/offline/book`​ (报名), `POST /exams/offline/cancel_booking`​ (取消)
- **验收标准**：考场与时间列表展示正确；名额与按钮状态准确；报名/取消流程顺畅，状态更新正确；多考场切换体验良好（如使用标签页）。

**4.2.15 功能名称：历史考试记录**

- **用户故事**：我希望能方便查看过去所有考试的记录和成绩。
- **功能描述**：从考试中心主页入口进入（建议新页面）。列表展示历史考试，含考试名称、类型、完成日期、最终成绩/得分、最终状态（已通过/未通过等）。默认时间倒序。支持分页加载。无记录则提示。
- **API接口需求 (示意)** ：`GET /api/exams/history`​ (分页)
- **验收标准**：入口正确；列表按序展示，信息准确；分页正常；空状态提示友好。

#### **模块六：个人中心**

**4.2.16 功能名称：个人中心主页布局与信息展示**

- **用户故事**：我希望在个人中心看到我的头像、昵称、认证状态，并能访问与我身份匹配的功能。
- **功能描述**：顶部（用户微信头像、昵称、认证状态/身份标识 - 据正式/未提交/待审核/不通过状态显示不同引导）；功能列表区（列表或宫格），通用功能（投诉建议、关于我们），根据状态显示（个人信息入口 - 名称和指向随状态变，证书管理 - 正式用户可见）。
- **验收标准**：顶部信息与状态显示正确；功能列表项据用户状态动态展示，权限控制正确。

**4.2.17 功能名称：个人信息详情 (正式用户)**

- **用户故事**：正式用户希望能查看自己脱敏后的完整个人信息和证书有效期。
- **功能描述**：正式用户从个人中心进入。只读列表展示：姓名（完整）、联系方式（手机号脱敏）、身份证号（脱敏）、隶属机构、职位、证书有效期（“至YYYY-MM-DD”）。**核心身份信息小程序端不提供修改入口，统一后台管理。**
- **API接口需求 (示意)** ：`GET /api/user/profile`​ (后端处理脱敏)
- **验收标准**：信息项显示正确；敏感信息脱敏；证书有效期显示正确；信息只读。

**4.2.18 功能名称：证书管理 (正式用户)**

- **用户故事**：我希望能查看当前证书、审批状态（若新证审批中且旧证有效，能同时看到两者）、及历史证书。
- **功能描述**：正式用户从个人中心进入。页面布局：**当前证书状态/最新证书区**（情况一：新证审批中，提示“审批中”，若此时上一张证书仍有效，则显示“您当前的有效证书：[名]，有效期至：[日期]”，并可查看；情况二：已有有效电子证书，直接展示预览或关键信息，可查看大图；情况三：无有效证书/已过期，提示相应信息）；**历史证书列表区**（展示已失效证书，含名称、获取日期、曾用有效期，可查看）。证书主要为图片形式，支持长按保存。
- **API接口需求 (示意)** ：`GET /api/user/certificates`​
- **验收标准**：根据实际证书情况正确显示当前状态区；新证审批中且旧证有效逻辑正确；证书图片加载与保存正常；历史证书展示与查看正常。

**4.2.19 功能名称：投诉与建议 (简化版)**

- **用户故事**：我希望能简单地输入内容提交我的意见或问题。
- **功能描述**：所有用户可见入口。提交界面：一个大的多行文本输入框（必填，可设字数限制），一个“提交反馈”按钮。后台自动记录提交用户ID和时间。成功后提示。
- **API接口需求 (示意)** ：`POST /api/feedback/submit`​ (参数: content)
- **验收标准**：界面简洁；反馈能成功提交；成功提示友好。

**4.2.20 功能名称：关于我们**

- **用户故事**：我希望能了解应用的基本信息。
- **功能描述**：所有用户可见入口。静态内容展示页，内容可后台配置或硬编码。含Logo、名称、版本号、公司/机构介绍、联系方式、版权信息等。
- **API接口需求 (示意)** ：若后台配置: `GET /api/app/about_us_info`​
- **验收标准**：页面显示正常，内容与设定一致。

## 5. 非功能需求

### 5.1 性能要求

- **小程序启动性能**：

  - 冷启动时间：主包加载完成到首页可交互时间应控制在 [3s] 以内（良好网络，主流机型）。
  - 热启动时间：小程序从后台切换到前台，恢复到可交互状态时间应控制在 [1s] 以内。
- **页面加载速度**：

  - 各主要列表页首屏数据加载完成时间应控制在 [2s] 以内。
  - 详情页加载时间应控制在 [1s] 以内。
- **API响应时间**：

  - 95%的请求应在 [500毫秒] 内返回响应（不含网络传输）。耗时操作应有明确加载提示。
- **资源占用**：
- **并发处理能力**：系统应能支持至少 [200] 用户同时在线常规操作。考试高峰期并发能力需与后端评估。
- **页面加载与渲染策略 (修订)** ：

  - **清晰的加载状态提示**：所有异步数据请求必须提供清晰、即时的加载状态指示（如页面级Loading、列表底部加载更多提示、按钮Loading状态）。
  - **感知性能优化**：优先渲染首屏可见内容。非即时需要的图片和组件采用懒加载。
  - **分包加载**：严格执行分包策略。首次访问分包时应有明确加载提示。
  - **缓存机制**：对不经常变动的数据建立合理缓存机制，优先本地读取，后台静默更新。访问缓存数据时确保体验平滑，避免闪烁。
  - **避免白屏和长时间无响应**：最大限度减少此情况。弱网下有超时友好提示和重试机制。

### 5.2 安全要求

- **数据传输安全**：HTTPS加密。敏感数据额外加密。
- **用户认证与授权**：Token安全存储与效期，严格权限校验防越权。
- **前端安全**：防范常见Web攻击，用户输入校验过滤，API防刷。
- **数据存储安全 (后端)** ：敏感信息加密存储，核心数据备份恢复。
- **防作弊相关安全**：防作弊信息安全上传，考试题目下发防窃取。
- **隐私保护**：遵守法规，协议明确告知，敏感权限用户授权。

### 5.3 兼容性要求

- **微信小程序端**：兼容主流iOS/Android最新及前2-3主版本；微信最新及近半年主版本；主流品牌机型；适应不同屏幕分辨率，UI不严重变形。
- **Web端 (预留)** ：如实现，兼容主流现代浏览器，响应式设计。

### 5.4 用户体验 (UX) 要求

- **界面风格**：与“疾控”机构专业、严谨、可信赖形象一致（如蓝、绿色系），美观大方，图标清晰，阅读舒适。
- **交互流畅性**：页面切换、动画（如有）自然不卡顿。用户操作有及时反馈（清晰加载提示、操作结果提示）。避免复杂或不直观流程。
- **导航清晰**：底部导航、返回按钮等清晰明确。
- **错误处理与提示**：用户操作错误、网络异常等有友好明确提示，并引导操作。
- **内容可读性**：信息主次分明，重要信息突出，排版合理。
- **加载过渡的平滑性**：数据加载完成并替换占位提示时，应确保过渡平滑，避免不必要的页面闪烁或布局抖动。图片懒加载时，使用合适占位符防布局抖动。
