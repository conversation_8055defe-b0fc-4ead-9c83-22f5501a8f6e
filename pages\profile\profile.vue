<template>
  <view class="profile-container">
    <!-- 用户信息头部 -->
    <view class="profile-header">
      <image class="avatar" :src="userStore.profile?.avatar || '/static/default-avatar.png'" />
      <view class="user-info">
        <text class="nickname">{{ userStore.profile?.nickname || '未设置昵称' }}</text>
        <view class="status-badge" :class="userStore.userStatus">
          {{ getStatusText() }}
        </view>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section">
      <!-- 个人信息相关 -->
      <view class="menu-group">
        <view class="menu-item" @tap="goToPersonalInfo">
          <view class="menu-icon">👤</view>
          <text class="menu-title">{{ getPersonalInfoTitle() }}</text>
          <view class="menu-arrow">></view>
        </view>
        
        <view v-if="userStore.isApproved" class="menu-item" @tap="goToCertificates">
          <view class="menu-icon">🏆</view>
          <text class="menu-title">证书管理</text>
          <view class="menu-arrow">></view>
        </view>
      </view>
      
      <!-- 通用功能 -->
      <view class="menu-group">
        <view class="menu-item" @tap="goToFeedback">
          <view class="menu-icon">💬</view>
          <text class="menu-title">投诉与建议</text>
          <view class="menu-arrow">></view>
        </view>
        
        <view class="menu-item" @tap="goToAbout">
          <view class="menu-icon">ℹ️</view>
          <text class="menu-title">关于我们</text>
          <view class="menu-arrow">></view>
        </view>
      </view>
      
      <!-- 退出登录 -->
      <view class="menu-group">
        <view class="menu-item logout-item" @tap="handleLogout">
          <view class="menu-icon">🚪</view>
          <text class="menu-title">退出登录</text>
        </view>
      </view>
    </view>
    
    <!-- 版本信息 -->
    <view class="version-info">
      <text class="version-text">版本 1.0.0</text>
    </view>
  </view>
</template>

<script setup lang="ts">
import { useUserStore } from '@/stores/modules/user';
import { storeToRefs } from 'pinia';

const userStore = useUserStore();
const { profile, userStatus, isApproved, isIncomplete, isPending, isRejected } = storeToRefs(userStore);

/**
 * 获取状态文本
 */
function getStatusText() {
  switch (userStore.userStatus) {
    case 'approved':
      return '已认证';
    case 'pending':
      return '审核中';
    case 'rejected':
      return '审核未通过';
    case 'incomplete':
      return '未完善资料';
    default:
      return '未知状态';
  }
}

/**
 * 获取个人信息菜单标题
 */
function getPersonalInfoTitle() {
  if (userStore.isIncomplete) {
    return '完善个人资料';
  } else if (userStore.isRejected) {
    return '修改个人资料';
  } else {
    return '个人信息';
  }
}

/**
 * 跳转到个人信息页面
 */
function goToPersonalInfo() {
  if (userStore.isApproved) {
    uni.navigateTo({ url: '/pages/profile/personal-info' });
  } else {
    uni.navigateTo({ url: '/pages/register/register' });
  }
}

/**
 * 跳转到证书管理
 */
function goToCertificates() {
  uni.navigateTo({ url: '/pages/profile/certificates' });
}

/**
 * 跳转到投诉建议
 */
function goToFeedback() {
  uni.navigateTo({ url: '/pages/profile/feedback' });
}

/**
 * 跳转到关于我们
 */
function goToAbout() {
  uni.navigateTo({ url: '/pages/profile/about' });
}

/**
 * 退出登录
 */
function handleLogout() {
  uni.showModal({
    title: '确认退出',
    content: '确定要退出登录吗？',
    success: (res) => {
      if (res.confirm) {
        userStore.clearProfile();
        uni.reLaunch({ url: '/pages/login/login' });
      }
    },
  });
}
</script>

<style lang="scss" scoped>
@import '@/styles/variables.scss';

.profile-container {
  min-height: 100vh;
  background-color: $background-color;
}

.profile-header {
  background: linear-gradient(135deg, $primary-color, $primary-light);
  padding: $spacing-xl $spacing-lg;
  display: flex;
  align-items: center;
  
  .avatar {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    margin-right: $spacing-lg;
    border: 4rpx solid rgba(255, 255, 255, 0.3);
  }
  
  .user-info {
    flex: 1;
    
    .nickname {
      display: block;
      font-size: $font-size-xl;
      font-weight: $font-weight-medium;
      color: white;
      margin-bottom: $spacing-sm;
    }
    
    .status-badge {
      display: inline-block;
      padding: 4rpx 12rpx;
      border-radius: $border-radius-small;
      font-size: $font-size-xs;
      color: white;
      
      &.approved {
        background-color: $success-color;
      }
      
      &.pending {
        background-color: $warning-color;
      }
      
      &.rejected {
        background-color: $error-color;
      }
      
      &.incomplete {
        background-color: $text-disabled;
      }
    }
  }
}

.menu-section {
  padding: $spacing-md;
  
  .menu-group {
    background-color: $surface-color;
    border-radius: $border-radius-medium;
    margin-bottom: $spacing-md;
    overflow: hidden;
    box-shadow: $shadow-light;
    
    .menu-item {
      display: flex;
      align-items: center;
      padding: $spacing-lg;
      border-bottom: 1rpx solid $divider-color;
      
      &:last-child {
        border-bottom: none;
      }
      
      &.logout-item {
        .menu-title {
          color: $error-color;
        }
      }
      
      .menu-icon {
        font-size: $font-size-xl;
        margin-right: $spacing-md;
      }
      
      .menu-title {
        flex: 1;
        font-size: $font-size-md;
        color: $text-primary;
      }
      
      .menu-arrow {
        font-size: $font-size-lg;
        color: $text-disabled;
      }
    }
  }
}

.version-info {
  text-align: center;
  padding: $spacing-lg;
  
  .version-text {
    font-size: $font-size-sm;
    color: $text-disabled;
  }
}
</style>
